// Anniversary Website JavaScript

// Wait for DOM to load
document.addEventListener('DOMContentLoaded', function() {
    // Show loading screen for 2 seconds for dramatic effect
    setTimeout(() => {
        document.getElementById('loading-screen').style.opacity = '0';
        setTimeout(() => {
            document.getElementById('loading-screen').style.display = 'none';
            initializeWebsite();
        }, 500);
    }, 2000);
});

function initializeWebsite() {
    setupNavigation();
    setupScrollAnimations();
    setupTimeline();
    setupLetters();
    setupGallery();
    setupPlaylist();
    load100Reasons();
    setupPromises();
    setupLightbox();
    setupMusicToggle();
}

// Navigation Setup
function setupNavigation() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    mobileMenuBtn.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
    });

    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
            
            // Close mobile menu if open
            mobileMenu.classList.add('hidden');
        });
    });

    // Hero button scroll to journey
    const heroButton = document.querySelector('#hero button');
    heroButton.addEventListener('click', () => {
        document.getElementById('journey').scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
    });
}

// Scroll Animations
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Observe all elements with animate-on-scroll class
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
}

// Timeline Setup
function setupTimeline() {
    const timelineContainer = document.querySelector('.timeline-container');
    
    const timelineData = [
        {
            date: "July 143rd, 2023",
            title: "The Day We Met",
            description: "The moment our eyes first met, I knew something magical was about to begin. You walked into my life like sunshine breaking through clouds, and everything changed forever."
        },
        {
            date: "July 150th, 2023",
            title: "Our First Real Conversation",
            description: "We talked for hours about everything and nothing. I discovered your beautiful mind, your dreams, and realized I wanted to know everything about you."
        },
        {
            date: "August 15th, 2023",
            title: "Our First Date",
            description: "Nervous butterflies, endless conversations over coffee, and the realization that I never wanted this evening to end. You wore that smile that made my heart skip."
        },
        {
            date: "September 2nd, 2023",
            title: "First Kiss",
            description: "Under the evening sky, time stood still as we shared our first kiss. It was everything I had dreamed it would be and more - pure magic."
        },
        {
            date: "September 20th, 2023",
            title: "Becoming Official",
            description: "The day you agreed to be mine officially. My heart was so full of joy, I thought it might burst. You said yes to being my girlfriend!"
        },
        {
            date: "October 31st, 2023",
            title: "First 'I Love You'",
            description: "On Halloween night, I told you I loved you for the first time. When you said it back, it was the sweetest treat I could ever ask for."
        },
        {
            date: "December 25th, 2023",
            title: "First Christmas Together",
            description: "Our first Christmas as a couple. Exchanging gifts, making memories, and realizing that every holiday is better with you by my side."
        },
        {
            date: "February 14th, 2024",
            title: "Valentine's Day Magic",
            description: "Our first Valentine's Day together. You made me understand what true love really feels like. Every day with you is Valentine's Day."
        },
        {
            date: "May 10th, 2024",
            title: "Meeting Your Family",
            description: "The day I met your family and they welcomed me with open arms. I knew then that I wanted to be part of your world forever."
        },
        {
            date: "July 143rd, 2024",
            title: "One Year Anniversary",
            description: "Today marks one incredible year of love, laughter, growth, and endless happiness. Here's to forever with you, my beautiful love."
        }
    ];

    timelineData.forEach((item, index) => {
        const timelineItem = document.createElement('div');
        timelineItem.className = 'timeline-item animate-on-scroll';
        timelineItem.innerHTML = `
            <div class="timeline-content">
                <h3 class="text-2xl font-dancing text-pink-600 mb-3">${item.title}</h3>
                <p class="text-gray-600 leading-relaxed">${item.description}</p>
            </div>
            <div class="timeline-date">${item.date}</div>
        `;
        timelineContainer.appendChild(timelineItem);
    });
}

// Letters Setup
function setupLetters() {
    const lettersContainer = document.querySelector('#letters .grid');
    
    const letters = [
        {
            title: "To My Beautiful Soul",
            preview: "My dearest love, every morning I wake up grateful for another day with you...",
            full: "My dearest love, every morning I wake up grateful for another day with you. Your smile is the first thing I want to see, and your laugh is the melody that makes my heart sing. You are not just my girlfriend, you are my best friend, my confidant, and my greatest love. In this one year together, you've shown me what it means to be truly happy. Thank you for being the incredible woman you are, and for choosing to love someone like me."
        },
        {
            title: "My Forever Promise",
            preview: "I promise to love you through every season of life...",
            full: "I promise to love you through every season of life, through the sunny days and the storms. I promise to support your dreams, celebrate your victories, and comfort you in difficult times. You are my forever, and I will spend every day proving that you made the right choice in loving me back. This first year has been just the beginning of our beautiful story."
        },
        {
            title: "What You Mean to Me",
            preview: "You are my sunshine on cloudy days, my anchor in rough seas...",
            full: "You are my sunshine on cloudy days, my anchor in rough seas, and my home wherever we are. You've taught me what true love feels like, and I am forever changed by your presence in my life. Every day with you is a gift I never want to take for granted. You make me want to be a better man, and you love me exactly as I am."
        },
        {
            title: "Our First Year Together",
            preview: "This year has been the most beautiful chapter of my life...",
            full: "This year has been the most beautiful chapter of my life. From our first nervous conversations to now, where I can't imagine a day without you. We've laughed together, grown together, and built something so special. You've been my partner in every adventure, my comfort in every worry, and my joy in every celebration."
        },
        {
            title: "Thank You, My Love",
            preview: "Thank you for choosing to love me, for seeing the best in me...",
            full: "Thank you for choosing to love me, for seeing the best in me even when I can't see it myself. Thank you for your patience when I'm being difficult, your kindness when I'm stressed, and your unwavering support in everything I do. Thank you for being you – the most amazing person I've ever known. Thank you for this incredible year."
        },
        {
            title: "You Are My Everything",
            preview: "In a world full of temporary things, you are my constant...",
            full: "In a world full of temporary things, you are my constant. You are my peace in chaos, my strength when I'm weak, and my joy in every moment. I love you more than words could ever express, and I will spend my lifetime showing you just how much you mean to me. Here's to many more years of loving you with all my heart."
        }
    ];

    letters.forEach((letter, index) => {
        const letterCard = document.createElement('div');
        letterCard.className = 'letter-card animate-on-scroll';
        letterCard.innerHTML = `
            <h3 class="text-xl font-dancing text-pink-600 mb-4">${letter.title}</h3>
            <div class="letter-preview">${letter.preview}</div>
            <div class="letter-full">${letter.full}</div>
            <button class="mt-4 text-pink-500 hover:text-pink-700 font-medium read-more-btn">Read More ❤️</button>
        `;
        
        const readMoreBtn = letterCard.querySelector('.read-more-btn');
        const preview = letterCard.querySelector('.letter-preview');
        const full = letterCard.querySelector('.letter-full');
        
        readMoreBtn.addEventListener('click', () => {
            if (full.style.display === 'none' || !full.style.display) {
                full.style.display = 'block';
                preview.style.display = 'none';
                readMoreBtn.textContent = 'Read Less ❤️';
            } else {
                full.style.display = 'none';
                preview.style.display = 'block';
                readMoreBtn.textContent = 'Read More ❤️';
            }
        });
        
        lettersContainer.appendChild(letterCard);
    });
}

// Gallery Setup
function setupGallery() {
    const galleryContainer = document.querySelector('.gallery-grid');
    
    // Placeholder images - replace with actual photos
    const galleryImages = [
        { src: 'https://via.placeholder.com/400x400/FFB6C1/FFFFFF?text=Our+First+Date+💕', alt: 'Our First Date - Coffee and endless conversations' },
        { src: 'https://via.placeholder.com/400x400/E6E6FA/FFFFFF?text=First+Kiss+✨', alt: 'The night of our first kiss' },
        { src: 'https://via.placeholder.com/400x400/F5F5DC/FFFFFF?text=Anniversary+Dinner+🥂', alt: 'Celebrating our milestones' },
        { src: 'https://via.placeholder.com/400x400/F0FFF0/FFFFFF?text=Adventure+Together+🌟', alt: 'Our hiking adventures' },
        { src: 'https://via.placeholder.com/400x400/FFB6C1/FFFFFF?text=Cozy+Nights+🏠', alt: 'Perfect nights in together' },
        { src: 'https://via.placeholder.com/400x400/E6E6FA/FFFFFF?text=Travel+Dreams+✈️', alt: 'Making memories everywhere' },
        { src: 'https://via.placeholder.com/400x400/F5F5DC/FFFFFF?text=Silly+Us+😄', alt: 'Being goofy together' },
        { src: 'https://via.placeholder.com/400x400/F0FFF0/FFFFFF?text=Special+Moments+💖', alt: 'Every day is special with you' },
        { src: 'https://via.placeholder.com/400x400/FFB6C1/FFFFFF?text=Christmas+Together+🎄', alt: 'Our first Christmas as a couple' },
        { src: 'https://via.placeholder.com/400x400/E6E6FA/FFFFFF?text=Valentine\'s+Day+💝', alt: 'Valentine\'s Day magic' },
        { src: 'https://via.placeholder.com/400x400/F5F5DC/FFFFFF?text=Lazy+Sundays+☀️', alt: 'Perfect lazy Sunday mornings' },
        { src: 'https://via.placeholder.com/400x400/F0FFF0/FFFFFF?text=One+Year+🎉', alt: 'Celebrating our first anniversary' }
    ];

    galleryImages.forEach((image, index) => {
        const galleryItem = document.createElement('div');
        galleryItem.className = 'gallery-item animate-on-scroll';
        galleryItem.innerHTML = `
            <img src="${image.src}" alt="${image.alt}" loading="lazy">
        `;
        
        galleryItem.addEventListener('click', () => {
            openLightbox(image.src, image.alt);
        });
        
        galleryContainer.appendChild(galleryItem);
    });
}

// Playlist Setup
function setupPlaylist() {
    const playlistContainer = document.querySelector('.playlist-container');
    
    const songs = [
        { title: "Perfect", artist: "Ed Sheeran", note: "Our song - reminds me of our first dance ❤️" },
        { title: "All of Me", artist: "John Legend", note: "Because you love all of me, even my edges" },
        { title: "Thinking Out Loud", artist: "Ed Sheeran", note: "Our late night conversation anthem" },
        { title: "A Thousand Years", artist: "Christina Perri", note: "Makes me think of forever with you" },
        { title: "Can't Help Myself", artist: "Four Tops", note: "Because I can't help loving you more each day" },
        { title: "Make You Feel My Love", artist: "Adele", note: "For those cozy rainy day moments" },
        { title: "At Last", artist: "Etta James", note: "How I felt when I found you" },
        { title: "La Vie En Rose", artist: "Édith Piaf", note: "Life is beautiful with you" },
        { title: "Marry Me", artist: "Train", note: "For our future dreams together" },
        { title: "Better Days", artist: "OneRepublic", note: "Every day is better with you" },
        { title: "Golden", artist: "Harry Styles", note: "You're golden, baby" },
        { title: "Adore You", artist: "Harry Styles", note: "I adore you, always" }
    ];

    songs.forEach((song, index) => {
        const playlistItem = document.createElement('div');
        playlistItem.className = 'playlist-item animate-on-scroll';
        playlistItem.innerHTML = `
            <div class="text-3xl text-pink-500">🎵</div>
            <div class="song-info flex-1">
                <h4>${song.title}</h4>
                <p>${song.artist}</p>
                <small class="text-pink-600 italic">${song.note}</small>
            </div>
            <button class="text-pink-500 hover:text-pink-700 text-xl">
                <i class="fas fa-play"></i>
            </button>
        `;
        playlistContainer.appendChild(playlistItem);
    });
}

// Load 100 Reasons from the text file content
function load100Reasons() {
    const reasonsGrid = document.getElementById('reasons-grid');

    // The 100 reasons extracted from 100.txt
    const reasons = [
        "Your smile brightens my day",
        "You always know how to make me laugh",
        "Your kindness inspires me",
        "You listen to me with genuine interest",
        "You support my dreams",
        "You have the most beautiful eyes",
        "You make me feel safe",
        "Your hugs are the best",
        "You are incredibly thoughtful",
        "You bring out the best in me",
        "You're my best friend",
        "You accept me as I am",
        "Your love for adventure",
        "The way you care for others",
        "Your creativity amazes me",
        "You make even the ordinary moments special",
        "Your sense of humor",
        "The way you look at me",
        "You are so patient",
        "You never give up on us",
        "You are my biggest cheerleader",
        "Your intelligence",
        "You're a great cook",
        "You're my confidant",
        "You surprise me in the best ways",
        "You're always up for trying new things",
        "Your optimism is contagious",
        "You're incredibly hardworking",
        "Your loyalty",
        "You make me feel appreciated",
        "Your laugh is infectious",
        "You make me a better person",
        "Your sense of adventure",
        "You're always there when I need you",
        "Your passion for life",
        "The way you hold my hand",
        "You're an amazing listener",
        "Your generosity",
        "You respect me",
        "You're honest with me",
        "You're my rock",
        "You make me feel cherished",
        "Your kisses are magical",
        "You're my favorite person",
        "Your resilience",
        "You're my partner in crime",
        "Your strength",
        "You make me feel alive",
        "Your dedication to our relationship",
        "You're always honest with me",
        "Your warm heart",
        "You make everyday brighter",
        "Your sense of style",
        "You're my soulmate",
        "Your sense of adventure",
        "You always have the best ideas",
        "You're my inspiration",
        "Your selflessness",
        "You understand me",
        "You make me feel special",
        "Your positive attitude",
        "You're so caring",
        "You're my comfort",
        "Your smile is contagious",
        "You're my partner in everything",
        "Your laugh makes me happy",
        "You're my safe haven",
        "Your spontaneity",
        "You make me feel loved",
        "You're incredibly thoughtful",
        "You always make time for me",
        "You're my favorite person to talk to",
        "You make me feel important",
        "Your loyalty means the world to me",
        "You're my best friend and lover",
        "You're my constant support",
        "Your warmth",
        "You make me feel understood",
        "You're my perfect match",
        "Your patience with me",
        "You're my biggest fan",
        "You make me laugh like no one else",
        "Your loving nature",
        "You make life fun",
        "You're always there for me",
        "Your thoughtfulness",
        "You make my heart skip a beat",
        "You're my everything",
        "Your encouragement",
        "You're my greatest adventure",
        "You make me feel at home",
        "Your tenderness",
        "You make me smile",
        "Your dedication to us",
        "You're my forever",
        "You make life worth living",
        "You're my reason to smile",
        "You make me feel complete",
        "Your love is my strength",
        "You're my one and only"
    ];

    reasons.forEach((reason, index) => {
        const reasonCard = document.createElement('div');
        reasonCard.className = 'reason-card';
        reasonCard.innerHTML = `
            <div class="reason-number">${index + 1}</div>
            <div class="reason-text">${reason}</div>
        `;
        reasonsGrid.appendChild(reasonCard);
    });

    // Animate cards on scroll
    const reasonCards = document.querySelectorAll('.reason-card');
    const reasonObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('visible');
                }, index * 100);
            }
        });
    }, { threshold: 0.1 });

    reasonCards.forEach(card => {
        reasonObserver.observe(card);
    });
}

// Promises Setup
function setupPromises() {
    const promisesContainer = document.querySelector('.promises-container');

    const promises = [
        {
            title: "I Promise to Love You Unconditionally",
            text: "Through every season of life, in joy and in sorrow, in sickness and in health, my love for you will remain constant and true. This first year has shown me that my love for you only grows stronger with each passing day."
        },
        {
            title: "I Promise to Support Your Dreams",
            text: "Whatever goals you set, whatever dreams you chase, I will be your biggest supporter and cheerleader every step of the way. Your success is my success, your happiness is my happiness."
        },
        {
            title: "I Promise to Grow With You",
            text: "As we both change and evolve, I promise to grow alongside you, learning and adapting so we can build a beautiful future together. We'll face every challenge hand in hand."
        },
        {
            title: "I Promise to Always Communicate",
            text: "I will always be honest with you, share my thoughts and feelings, and listen to yours with an open heart and mind. No secrets, no walls - just us, always."
        },
        {
            title: "I Promise to Make You Laugh",
            text: "Even on the hardest days, I promise to find ways to bring a smile to your face and joy to your heart. Your laughter is my favorite sound in the world."
        },
        {
            title: "I Promise to Choose You Every Day",
            text: "Every morning I wake up, I promise to choose you again, to love you again, and to build our life together with intention and care. Today, tomorrow, and always."
        },
        {
            title: "I Promise to Cherish Every Moment",
            text: "I promise to never take our time together for granted. Every hug, every kiss, every conversation - I'll treasure them all as the precious gifts they are."
        },
        {
            title: "I Promise to Be Your Safe Haven",
            text: "I promise to always be your safe place to land, your comfort in storms, and your peace in chaos. With me, you'll always be home."
        }
    ];

    promises.forEach((promise, index) => {
        const promiseCard = document.createElement('div');
        promiseCard.className = 'promise-card animate-on-scroll';
        promiseCard.innerHTML = `
            <h3 class="text-2xl font-dancing text-pink-600 mb-4">${promise.title}</h3>
            <p class="text-gray-600 leading-relaxed text-lg">${promise.text}</p>
        `;
        promisesContainer.appendChild(promiseCard);
    });
}

// Lightbox Setup
function setupLightbox() {
    const lightbox = document.getElementById('lightbox');
    const lightboxImg = document.getElementById('lightbox-img');
    const lightboxClose = document.getElementById('lightbox-close');

    lightboxClose.addEventListener('click', closeLightbox);
    lightbox.addEventListener('click', (e) => {
        if (e.target === lightbox) {
            closeLightbox();
        }
    });

    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    });
}

function openLightbox(src, alt) {
    const lightbox = document.getElementById('lightbox');
    const lightboxImg = document.getElementById('lightbox-img');

    lightboxImg.src = src;
    lightboxImg.alt = alt;
    lightbox.classList.remove('hidden');
    lightbox.classList.add('flex');
    document.body.style.overflow = 'hidden';
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.classList.add('hidden');
    lightbox.classList.remove('flex');
    document.body.style.overflow = 'auto';
}

// Music Toggle Setup
function setupMusicToggle() {
    const musicToggle = document.getElementById('music-toggle');
    let isPlaying = false;

    musicToggle.addEventListener('click', () => {
        if (isPlaying) {
            // Pause music logic here
            musicToggle.innerHTML = '<i class="fas fa-music"></i>';
            isPlaying = false;
        } else {
            // Play music logic here
            musicToggle.innerHTML = '<i class="fas fa-pause"></i>';
            isPlaying = true;
        }
    });
}

// Utility function for smooth scrolling
function smoothScrollTo(element) {
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}
