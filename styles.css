/* Custom CSS Variables for Pastel Color Scheme */
:root {
    --blush-pink: #FFB6C1;
    --lavender: #E6E6FA;
    --cream: #F5F5DC;
    --mint-green: #F0FFF0;
    --soft-pink: #FFC0CB;
    --light-purple: #DDA0DD;
    --warm-white: #FEFEFE;
}

/* Font Families */
.font-dancing {
    font-family: 'Dancing Script', cursive;
}

.font-great-vibes {
    font-family: 'Great Vibes', cursive;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--cream);
}

::-webkit-scrollbar-thumb {
    background: var(--blush-pink);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--soft-pink);
}

/* Floating Hearts Animation */
.floating-hearts {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.heart {
    position: absolute;
    font-size: 20px;
    animation: float-hearts 15s infinite linear;
    opacity: 0.6;
}

.heart-1 { left: 10%; animation-delay: 0s; }
.heart-2 { left: 20%; animation-delay: 2s; }
.heart-3 { left: 30%; animation-delay: 4s; }
.heart-4 { left: 70%; animation-delay: 6s; }
.heart-5 { left: 80%; animation-delay: 8s; }
.heart-6 { left: 90%; animation-delay: 10s; }

@keyframes float-hearts {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Floating Animation for Decorative Elements */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Fade In Up Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 1s ease-out forwards;
    opacity: 0;
}

/* Animation Delays */
.animation-delay-300 { animation-delay: 0.3s; }
.animation-delay-500 { animation-delay: 0.5s; }
.animation-delay-600 { animation-delay: 0.6s; }
.animation-delay-900 { animation-delay: 0.9s; }
.animation-delay-1000 { animation-delay: 1s; }
.animation-delay-1200 { animation-delay: 1.2s; }

/* Scroll Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-out;
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Navigation Styles */
#navbar {
    border-radius: 0 0 20px 20px;
}

.nav-link {
    position: relative;
    font-weight: 500;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background: linear-gradient(90deg, var(--blush-pink), var(--light-purple));
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 100%;
}

/* Timeline Styles */
.timeline-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline-container::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, var(--blush-pink), var(--lavender));
    transform: translateX(-50%);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    margin: 40px 0;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.timeline-item.visible {
    opacity: 1;
    transform: translateY(0);
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-right: 50%;
    padding-right: 40px;
    text-align: right;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: 50%;
    padding-left: 40px;
}

.timeline-content {
    background: white;
    padding: 25px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    position: relative;
    border: 2px solid var(--cream);
}

.timeline-date {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, var(--blush-pink), var(--lavender));
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Letter Cards */
.letter-card {
    background: white;
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 2px solid var(--cream);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.letter-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--blush-pink), var(--lavender));
}

.letter-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.letter-preview {
    color: #666;
    line-height: 1.6;
}

.letter-full {
    display: none;
    color: #666;
    line-height: 1.8;
    margin-top: 15px;
}

/* Gallery Grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 40px;
}

.gallery-item {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    aspect-ratio: 1;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 3px solid var(--cream);
}

.gallery-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,182,193,0.1), rgba(230,230,250,0.1));
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover::before {
    opacity: 1;
}

.gallery-item:hover {
    transform: scale(1.05) rotate(1deg);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-item:hover img {
    filter: brightness(1.1) saturate(1.1);
}

/* Playlist Styles */
.playlist-item {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    border: 2px solid var(--cream);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 20px;
}

.playlist-item:hover {
    transform: translateX(10px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.song-info h4 {
    color: #333;
    font-weight: 600;
    margin-bottom: 5px;
}

.song-info p {
    color: #666;
    font-size: 14px;
}

/* 100 Reasons Grid */
.reasons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 40px;
}

.reason-card {
    background: white;
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 2px solid var(--cream);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(30px);
}

.reason-card.visible {
    opacity: 1;
    transform: translateY(0);
}

.reason-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--blush-pink), var(--mint-green), var(--lavender));
}

.reason-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.reason-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--blush-pink);
    margin-bottom: 10px;
}

.reason-text {
    color: #555;
    line-height: 1.6;
    font-size: 16px;
}

/* Promise Cards */
.promise-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 2px solid var(--cream);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.promise-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--lavender), var(--blush-pink));
}

.promise-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
    .timeline-container::before {
        left: 20px;
    }
    
    .timeline-item:nth-child(odd) .timeline-content,
    .timeline-item:nth-child(even) .timeline-content {
        margin: 0;
        margin-left: 50px;
        padding: 20px;
        text-align: left;
    }
    
    .timeline-date {
        left: 20px;
        transform: translateY(-50%);
    }
    
    .reasons-grid {
        grid-template-columns: 1fr;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--cream);
    border-radius: 50%;
    border-top-color: var(--blush-pink);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Lightbox Styles */
#lightbox {
    backdrop-filter: blur(5px);
}

#lightbox-img {
    box-shadow: 0 20px 60px rgba(0,0,0,0.5);
}

/* Music Toggle Button */
#music-toggle {
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.2);
}

#music-toggle:hover {
    background: linear-gradient(135deg, var(--blush-pink), var(--lavender));
}

/* Loading Screen */
#loading-screen {
    transition: opacity 0.5s ease-out;
}

/* Utility Classes */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.gradient-text {
    background: linear-gradient(135deg, var(--blush-pink), var(--lavender));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
