<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="A beautiful anniversary website celebrating our love story">
    <meta property="og:title" content="Our Love Story - Anniversary Website">
    <meta property="og:description" content="A romantic journey through our beautiful relationship">
    <meta property="og:type" content="website">
    
    <title>Our Love Story - Anniversary</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&family=Poppins:wght@300;400;500;600&family=Great+Vibes&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gradient-to-br from-pink-50 to-purple-50 overflow-x-hidden">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-gradient-to-br from-pink-100 to-purple-100 z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="text-6xl font-great-vibes text-pink-600 mb-8 animate-pulse">
                Loading Our Love Story...
            </div>
            <div class="flex justify-center space-x-2">
                <div class="w-4 h-4 bg-pink-400 rounded-full animate-bounce"></div>
                <div class="w-4 h-4 bg-purple-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-4 h-4 bg-pink-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
        </div>
    </div>
    <!-- Floating Hearts Background -->
    <div class="floating-hearts">
        <div class="heart heart-1">💖</div>
        <div class="heart heart-2">💕</div>
        <div class="heart heart-3">💗</div>
        <div class="heart heart-4">💖</div>
        <div class="heart heart-5">💕</div>
        <div class="heart heart-6">💗</div>
    </div>

    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-white/80 backdrop-blur-md z-50 shadow-lg transition-all duration-300" id="navbar">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="text-2xl font-bold text-pink-600 font-dancing">Our Love Story</div>
                <div class="hidden md:flex space-x-6">
                    <a href="#hero" class="nav-link text-gray-700 hover:text-pink-600 transition-colors duration-300">Home</a>
                    <a href="#journey" class="nav-link text-gray-700 hover:text-pink-600 transition-colors duration-300">Journey</a>
                    <a href="#letters" class="nav-link text-gray-700 hover:text-pink-600 transition-colors duration-300">Letters</a>
                    <a href="#gallery" class="nav-link text-gray-700 hover:text-pink-600 transition-colors duration-300">Gallery</a>
                    <a href="#playlist" class="nav-link text-gray-700 hover:text-pink-600 transition-colors duration-300">Playlist</a>
                    <a href="#reasons" class="nav-link text-gray-700 hover:text-pink-600 transition-colors duration-300">100 Reasons</a>
                    <a href="#promises" class="nav-link text-gray-700 hover:text-pink-600 transition-colors duration-300">Promises</a>
                </div>
                <button class="md:hidden text-pink-600" id="mobile-menu-btn">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div class="md:hidden bg-white/95 backdrop-blur-md hidden" id="mobile-menu">
            <div class="px-4 py-2 space-y-2">
                <a href="#hero" class="block py-2 text-gray-700 hover:text-pink-600 transition-colors duration-300">Home</a>
                <a href="#journey" class="block py-2 text-gray-700 hover:text-pink-600 transition-colors duration-300">Journey</a>
                <a href="#letters" class="block py-2 text-gray-700 hover:text-pink-600 transition-colors duration-300">Letters</a>
                <a href="#gallery" class="block py-2 text-gray-700 hover:text-pink-600 transition-colors duration-300">Gallery</a>
                <a href="#playlist" class="block py-2 text-gray-700 hover:text-pink-600 transition-colors duration-300">Playlist</a>
                <a href="#reasons" class="block py-2 text-gray-700 hover:text-pink-600 transition-colors duration-300">100 Reasons</a>
                <a href="#promises" class="block py-2 text-gray-700 hover:text-pink-600 transition-colors duration-300">Promises</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="min-h-screen flex items-center justify-center relative overflow-hidden">
        <div class="text-center z-10 px-4 max-w-4xl mx-auto">
            <h1 class="text-6xl md:text-8xl font-great-vibes text-pink-600 mb-6 animate-fade-in-up">
                Our Love Story
            </h1>
            <div class="text-2xl md:text-3xl font-dancing text-gray-700 mb-8 animate-fade-in-up animation-delay-300">
                Sarthak & My Beautiful Love
            </div>
            <div class="text-lg md:text-xl text-gray-600 mb-8 animate-fade-in-up animation-delay-600">
                Celebrating 1 magical year together
            </div>
            <div class="text-base md:text-lg text-gray-500 mb-12 animate-fade-in-up animation-delay-900">
                July 143rd - The day that changed everything
            </div>
            <button class="bg-gradient-to-r from-pink-400 to-purple-400 text-white px-8 py-4 rounded-full text-lg font-medium hover:from-pink-500 hover:to-purple-500 transition-all duration-300 transform hover:scale-105 animate-fade-in-up animation-delay-1200 shadow-lg">
                Begin Our Journey
            </button>
        </div>
        
        <!-- Decorative elements -->
        <div class="absolute top-20 left-10 text-6xl text-pink-200 animate-float">💕</div>
        <div class="absolute bottom-20 right-10 text-4xl text-purple-200 animate-float animation-delay-500">💖</div>
        <div class="absolute top-1/2 left-5 text-3xl text-pink-300 animate-float animation-delay-1000">✨</div>
    </section>

    <!-- Our Journey Section -->
    <section id="journey" class="py-20 px-4">
        <div class="container mx-auto max-w-6xl">
            <h2 class="text-5xl font-dancing text-center text-gray-800 mb-16 animate-on-scroll">Our Journey Together</h2>
            
            <div class="timeline-container">
                <!-- Timeline items will be added via JavaScript -->
            </div>
        </div>
    </section>

    <!-- Letters Section -->
    <section id="letters" class="py-20 px-4 bg-gradient-to-r from-pink-50 to-purple-50">
        <div class="container mx-auto max-w-6xl">
            <h2 class="text-5xl font-dancing text-center text-gray-800 mb-16 animate-on-scroll">Letters from My Heart</h2>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Letter cards will be added via JavaScript -->
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section id="gallery" class="py-20 px-4">
        <div class="container mx-auto max-w-6xl">
            <h2 class="text-5xl font-dancing text-center text-gray-800 mb-16 animate-on-scroll">Our Beautiful Memories</h2>
            
            <div class="gallery-grid">
                <!-- Gallery items will be added via JavaScript -->
            </div>
        </div>
    </section>

    <!-- Playlist Section -->
    <section id="playlist" class="py-20 px-4 bg-gradient-to-r from-purple-50 to-pink-50">
        <div class="container mx-auto max-w-4xl">
            <h2 class="text-5xl font-dancing text-center text-gray-800 mb-16 animate-on-scroll">Our Soundtrack</h2>
            
            <div class="playlist-container">
                <!-- Playlist items will be added via JavaScript -->
            </div>
        </div>
    </section>

    <!-- 100 Reasons Section -->
    <section id="reasons" class="py-20 px-4">
        <div class="container mx-auto max-w-7xl">
            <h2 class="text-5xl font-dancing text-center text-gray-800 mb-8 animate-on-scroll">100 Reasons Why I Love You</h2>
            <p class="text-center text-gray-600 mb-16 text-lg">Every single thing about you that makes my heart flutter</p>
            
            <div class="reasons-grid" id="reasons-grid">
                <!-- 100 reasons will be loaded here -->
            </div>
        </div>
    </section>

    <!-- Promises Section -->
    <section id="promises" class="py-20 px-4 bg-gradient-to-r from-pink-50 to-purple-50">
        <div class="container mx-auto max-w-4xl">
            <h2 class="text-5xl font-dancing text-center text-gray-800 mb-16 animate-on-scroll">My Promises to You</h2>
            
            <div class="promises-container">
                <!-- Promise cards will be added via JavaScript -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12 px-4">
        <div class="container mx-auto max-w-4xl text-center">
            <div class="text-3xl font-dancing mb-4">Forever and Always</div>
            <div class="text-gray-400 mb-2">Made with 💖 by Sarthak for the love of my life</div>
            <div class="text-sm text-gray-500">One year down, forever to go ✨</div>
        </div>
    </footer>

    <!-- Lightbox Modal -->
    <div id="lightbox" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden items-center justify-center p-4">
        <div class="relative max-w-4xl max-h-full">
            <img id="lightbox-img" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg">
            <button id="lightbox-close" class="absolute top-4 right-4 text-white text-3xl hover:text-pink-400 transition-colors">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Audio Control -->
    <div class="fixed bottom-6 right-6 z-40">
        <button id="music-toggle" class="bg-pink-500 text-white p-4 rounded-full shadow-lg hover:bg-pink-600 transition-all duration-300 transform hover:scale-110">
            <i class="fas fa-music"></i>
        </button>
    </div>

    <script src="script.js"></script>
</body>
</html>
