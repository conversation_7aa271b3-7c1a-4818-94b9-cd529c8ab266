<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💕 Our First Anniversary Love Journey</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Background Effects -->
    <div class="background-effects">
        <div class="floating-hearts"></div>
        <div class="background-patterns"></div>
    </div>

    <!-- Love Meter -->
    <div class="love-meter" id="loveMeter">
        <div class="love-meter-content">
            <div class="love-meter-header">
                <span class="love-meter-title">💕 Love Journey</span>
                <span class="love-meter-score" id="loveScore">0 points</span>
            </div>
            <div class="love-meter-bar">
                <div class="love-meter-fill" id="loveFill"></div>
            </div>
            <div class="love-meter-info">
                <span class="level-info" id="levelInfo">Level 1 of 6</span>
                <span class="current-level" id="currentLevel">Welcome 💖</span>
            </div>
        </div>
    </div>

    <!-- Main App Container -->
    <div class="app-container" id="app">
        <!-- Welcome Screen -->
        <div class="screen welcome-screen active" id="welcomeScreen">
            <div class="card romantic-card">
                <h1 class="gradient-text">💕 Welcome to Our Love Journey</h1>
                <h2 class="nepali-text">हाम्रो माया यात्रामा स्वागत छ!</h2>
                <p class="welcome-message">
                    Today marks our first beautiful year together! 🌟<br>
                    Join me on this interactive journey through our most precious memories.
                </p>
                <div class="cultural-elements">
                    <span class="cultural-icon">🏔️</span>
                    <span class="cultural-icon">🌸</span>
                    <span class="cultural-icon">🕉️</span>
                </div>
                <button class="btn btn-romantic" onclick="startJourney()">
                    Begin Our Story ✨
                </button>
            </div>
        </div>

        <!-- Beginning Story Screen -->
        <div class="screen story-screen" id="storyScreen">
            <div class="card dreamy-card">
                <h2 class="gradient-text">📖 Our Beginning</h2>
                <div class="story-content" id="storyContent">
                    <!-- Story pages will be populated by JavaScript -->
                </div>
                <div class="story-navigation">
                    <button class="btn btn-secondary" id="prevStory" onclick="previousStoryPage()" disabled>
                        ← Previous
                    </button>
                    <span class="page-indicator" id="pageIndicator">1 / 3</span>
                    <button class="btn btn-primary" id="nextStory" onclick="nextStoryPage()">
                        Next →
                    </button>
                </div>
            </div>
        </div>

        <!-- Memory Game Screen -->
        <div class="screen memory-game-screen" id="memoryGameScreen">
            <div class="card glass-card">
                <h2 class="gradient-text">🌟 Collect Our Memories</h2>
                <p class="game-instructions">
                    <span class="nepali-text">सम्झनाहरू</span> - Catch the floating memories before time runs out!
                </p>
                <div class="game-stats">
                    <div class="stat">
                        <span class="stat-label">Time:</span>
                        <span class="stat-value" id="gameTimer">30</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Memories:</span>
                        <span class="stat-value" id="memoriesCollected">0/10</span>
                    </div>
                </div>
                <div class="game-area" id="gameArea">
                    <!-- Memory items will be spawned here -->
                </div>
                <button class="btn btn-romantic" id="startGameBtn" onclick="startMemoryGame()">
                    Start Game 🎮
                </button>
            </div>
        </div>

        <!-- Quiz Challenge Screen -->
        <div class="screen quiz-screen" id="quizScreen">
            <div class="card romantic-card">
                <h2 class="gradient-text">💝 Love Quiz Challenge</h2>
                <div class="quiz-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="quizProgress"></div>
                    </div>
                    <span class="question-counter" id="questionCounter">Question 1 of 3</span>
                </div>
                <div class="quiz-content" id="quizContent">
                    <!-- Quiz questions will be populated here -->
                </div>
            </div>
        </div>

        <!-- Photo Gallery Screen -->
        <div class="screen photo-screen" id="photoScreen">
            <div class="card glass-card">
                <h2 class="gradient-text">📸 Our Memory Gallery</h2>
                <p class="gallery-description">
                    <span class="nepali-text">तस्बिरहरू</span> - Click on each memory to relive our beautiful moments!
                </p>
                <div class="photo-gallery" id="photoGallery">
                    <!-- Photo memories will be populated here -->
                </div>
                <div class="gallery-stats">
                    <span id="viewedCount">Viewed: 0/6 memories</span>
                </div>
                <button class="btn btn-romantic" id="continueFromGallery" onclick="goToCelebration()" disabled>
                    Complete Journey! 🎊
                </button>
            </div>
        </div>

        <!-- Celebration Screen -->
        <div class="screen celebration-screen" id="celebrationScreen">
            <div class="card romantic-card">
                <h1 class="gradient-text celebration-title">🎊 Happy 1st Anniversary! 🎊</h1>
                <h2 class="nepali-text">हाम्रो पहिलो वर्षगाँठको शुभकामना! 💕</h2>
                <p class="celebration-message">
                    You've completed our love journey adventure! Here's to many more years of
                    laughter, love, and beautiful memories together.<br>
                    <span class="nepali-text">तिमी मेरो जीवनको सबैभन्दा राम्रो उपहार हौ! 💖</span>
                </p>
                <div class="final-stats" id="finalStats">
                    <!-- Final statistics will be populated here -->
                </div>
                <div class="achievements" id="achievements">
                    <!-- Achievements will be populated here -->
                </div>
                <div class="celebration-actions">
                    <button class="btn btn-romantic" onclick="restartJourney()">
                        Play Again 🔄
                    </button>
                    <button class="btn btn-dreamy" onclick="shareJourney()">
                        Share Our Love 💌
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Confetti Container -->
    <div class="confetti-container" id="confettiContainer"></div>

    <script src="script.js"></script>
</body>
</html>
